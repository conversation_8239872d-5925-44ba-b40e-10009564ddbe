
WanTiny_RegisterModule("Buttonstorage", function()

local ADDON_NAME = "WanTiny" -- 插件名称
local VERSION = "2.0.0"      -- 插件版本

-- 常量定义
local BTN_SIZE = 24                -- 收纳面板内按钮默认尺寸
local PADDING = 2                  -- 按钮间距
local BORDER = 6                   -- 收纳面板边框宽度
local MAIN_BTN_SIZE = 33           -- 主按钮尺寸
local MAIN_BTN_ICON_SIZE = 23      -- 主按钮图标尺寸
local MAIN_BTN_BORDER_SIZE = 56    -- 主按钮边框尺寸
local MINIMAP_RADIUS_OFFSET = 2    -- 小地图半径偏移
local DRAG_LIMIT_PADDING = 16      -- 拖拽边界安全距离
local BACKDROP_TEMPLATE = {        -- 通用背景模板
    bgFile = "Interface/Tooltips/UI-Tooltip-Background",
    edgeFile = "Interface/Tooltips/UI-Tooltip-Border",
    tile = true, tileSize = 16, edgeSize = 16,
    insets = {left = 4, right = 4, top = 4, bottom = 4}
}
-- ===================================================

-- 创建带背景的Frame（支持自定义参数）
local function CreateFrameWithBackdrop(parent, name, size, opts)
    opts = opts or {}
    local frame = CreateFrame("Frame", name, parent, "BackdropTemplate")
    if size then frame:SetSize(size[1], size[2]) end
    frame:SetBackdrop(opts.backdrop or BACKDROP_TEMPLATE)
    frame:SetBackdropColor(0, 0, 0, opts.backdropAlpha or 0.8)
    frame:SetBackdropBorderColor(0.5, 0.5, 0.5, 1)
    if opts.strata then frame:SetFrameStrata(opts.strata) end
    if opts.movable then frame:SetMovable(true) end
    if opts.enableMouse then frame:EnableMouse(true) end
    if opts.frameLevel then frame:SetFrameLevel(opts.frameLevel) end
    return frame
end

-- 初始化配置表（默认值）
local function InitConfig()
    WanTinyDB = WanTinyDB or {}
    WanTinyDB.Map = WanTinyDB.Map or {}
    local defaults = {
        MinimapPos = 45,
        MinimapPointMode = 2,
        MinimapPointXY = {0, 0},
        MiniButShouNa_YN = 1,
        MiniButPerRow = 6,
        MiniButHideDelay = 1.5,
        MiniButExcludedButtons = {},
    }
    for key, value in pairs(defaults) do
        if WanTinyDB.Map[key] == nil then
            WanTinyDB.Map[key] = value
        end
    end
end

-- 主按钮配置表
local buttonConfig = {
    name = "WanTiny_MiniMapBut",
    parent = UIParent,
    size = {MAIN_BTN_SIZE, MAIN_BTN_SIZE},
    properties = {
        {"SetMovable", true},
        {"EnableMouse", true},
        {"RegisterForClicks", "LeftButtonUp", "RightButtonUp"},
        {"RegisterForDrag", "LeftButton"},
        {"SetFrameStrata", "MEDIUM"}
    },
    textures = {
        {name = "Border", layer = "BORDER"},
        {name = "icon", layer = "BACKGROUND", texture = "Interface/Icons/inv_valentinesboxofchocolates02", point = {"CENTER", 0, 0}},
        {name = "error", layer = "BORDER", texture = "interface/common/voicechat-muted.blp", size = {18, 18}, alpha = 0.7, point = {"CENTER", 0, 0}, hide = true}
    }
}

-- 通用控件创建函数，支持 Button/CheckButton/Frame 等类型，支持回调
local function CreateConfiguredButton(config)
    local button = CreateFrame(config.type or "Button", config.name, config.parent)
    if config.size then button:SetSize(config.size[1], config.size[2]) end
    for _, prop in ipairs(config.properties or {}) do
        local method, a, b, c = prop[1], prop[2], prop[3], prop[4]
        if method == "RegisterForClicks" and type(a) == "string" and b then
            button:RegisterForClicks(a, b)
        elseif method == "RegisterForDrag" and type(a) == "string" then
            button:RegisterForDrag(a)
        elseif button[method] then
            button[method](button, a, b, c)
        end
    end
    button:SetFrameLevel(config.frameLevel or (button:GetFrameLevel() + 1))
    for _, tex in ipairs(config.textures or {}) do
        button[tex.name] = button:CreateTexture(nil, tex.layer)
        if tex.texture then button[tex.name]:SetTexture(tex.texture) end
        if tex.size then button[tex.name]:SetSize(tex.size[1], tex.size[2]) end
        if tex.alpha then button[tex.name]:SetAlpha(tex.alpha) end
        if tex.point then button[tex.name]:SetPoint(tex.point[1], tex.point[2], tex.point[3]) end
        if tex.hide then button[tex.name]:Hide() end
    end
    return button
end

-- 创建主按钮
local MiniMapBut = CreateConfiguredButton(buttonConfig)
-- 暴露为全局变量供WanTinyUI.lua访问
_G.WanTiny_MiniMapBut = MiniMapBut

-- 提示函数
local function ShowAddonsTooltip(self, origin)
    GameTooltip:ClearLines()
    if origin then
        GameTooltip:SetOwner(self, "ANCHOR_BOTTOMLEFT", -2, 16)
    else
        GameTooltip:SetOwner(self, "ANCHOR_TOPRIGHT", -24, 0)
    end
    GameTooltip:AddLine("|cffFF00FF" .. ADDON_NAME .. "|r-" .. VERSION)
    GameTooltip:AddLine("|cff00ff00左键：展开/收起收纳面板|r")
    GameTooltip:AddLine("|cffffcc00右键：打开设置面板|r")
    GameTooltip:AddLine("|cffffff00Ctrl+左键：收纳/移出小地图按钮|r")
    GameTooltip:AddLine("|cffff2020Shift+左键：重载界面|r")
    GameTooltip:Show()
end

MiniMapBut:SetScript("OnEnter", function(self)
    ShowAddonsTooltip(self)
end)

MiniMapBut:SetScript("OnLeave", function()
    GameTooltip:ClearLines()
    GameTooltip:Hide()
end)

-- 位置计算和更新函数
local function UpdateButtonPosition(mode, xpos, ypos)
    if mode == 1 then
        MiniMapBut:ClearAllPoints()
        local angle = xpos or WanTinyDB.Map.MinimapPos
        local radius = Minimap:GetWidth() / 2 + MINIMAP_RADIUS_OFFSET
        local x = radius * math.cos(math.rad(angle))
        local y = radius * math.sin(math.rad(angle))
        MiniMapBut:SetPoint("CENTER", Minimap, "CENTER", x, y)
        WanTinyDB.Map.MinimapPos = angle
    elseif mode == 3 then
        MiniMapBut:ClearAllPoints()
        local x = xpos or WanTinyDB.Map.MinimapPointXY[1]
        local y = ypos or WanTinyDB.Map.MinimapPointXY[2]
        MiniMapBut:SetPoint("CENTER", UIParent, "CENTER", x, y)
        WanTinyDB.Map.MinimapPointXY[1] = x
        WanTinyDB.Map.MinimapPointXY[2] = y
    end
end



local function UpdateButtonPosition_OnUpdate()
    local mode = WanTinyDB.Map.MinimapPointMode
    if mode == 1 then
        local scale = Minimap:GetEffectiveScale() or 1
        local cx, cy = Minimap:GetCenter()
        local cursorX, cursorY = GetCursorPosition()
        cursorX, cursorY = cursorX / scale, cursorY / scale
        local angle = math.deg(math.atan2(cursorY - cy, cursorX - cx))
        if angle < 0 then angle = angle + 360 end
        UpdateButtonPosition(mode, angle)
    elseif mode == 3 then
        local UIScale = UIParent:GetEffectiveScale()
        local cursorX, cursorY = GetCursorPosition()
        cursorX, cursorY = cursorX / UIScale, cursorY / UIScale
        local screenWidth, screenHeight = GetScreenWidth() * 0.5, GetScreenHeight() * 0.5
        local xpos = math.max(-screenWidth + DRAG_LIMIT_PADDING, math.min(screenWidth - DRAG_LIMIT_PADDING, cursorX - screenWidth))
        local ypos = math.max(-screenHeight + DRAG_LIMIT_PADDING, math.min(screenHeight - DRAG_LIMIT_PADDING, cursorY - screenHeight))
        UpdateButtonPosition(mode, xpos, ypos)
    end
end

-- 收纳和还原函数（需提前定义，供设置面板回调使用）
-- ShowAllCollectedButtons 声明为全局函数供WanTinyUI.lua调用
local RestoreAllCollectedButtons
local ShowAllCollectedButtons


-- 检查按钮有效性（防止外部插件破坏）
local function IsValidButton(btn)
    return btn and type(btn.IsObjectType) == "function" and btn:IsObjectType("Button")
end

-- 还原所有收纳的按钮
RestoreAllCollectedButtons = function()
    if not MiniMapBut or not MiniMapBut.CollectFrame then return end
    for _, btn in ipairs(MiniMapBut.CollectFrame.buttons or {}) do
        if IsValidButton(btn) and btn._oldParent then
            btn:SetParent(btn._oldParent)
            if btn._oldPoint then btn:ClearAllPoints(); btn:SetPoint(unpack(btn._oldPoint)) end
            if btn._oldSize then btn:SetSize(btn._oldSize[1], btn._oldSize[2]) end
            btn:SetAlpha(1)
        end
    end
    MiniMapBut.CollectFrame.buttons = {}
end


-- UI 配置表与生成逻辑分离
-- 设置面板代码已移至WanTinyUI.lua
-- 忽略的小地图按钮模式
local ignorePatterns = {
    "MiniMap", "Minimap", "BookOfTracksFrame", "GatherNote", "FishingExtravaganzaMini", "MiniNotePOI",
    "RecipeRadarMinimapIcon", "FWGMinimapPOI", "CartographerNotesPOI", "MBB_MinimapButtonFrame",
    "EnhancedFrameMinimapButton", "GFW_Track", "TDial_Track", "GatherMatePin", "HandyNotes_.*Pin",
    "TimeManagerClockButton", "GameTimeFrame", "DA_Minimap", "ElvConfigToggle", "GuildInstanceDifficulty",
    "QueueStatus", "GatherArchNote", "ZGVMarker", "QuestPointerPOI", "poiMinimap", "PremadeFilter_MinimapButton",
    "QuestieFrame", "Guidelime", "WanTiny_MiniMapBut", "NWBNaxxMarkerMini", "NWBMini", "SexyMap",
    "ElvUI_MinimapHolder", "RecycleBinToggleButton", "BFGPSButton", "LibDBIcon10_HandyNotes_NPCs",
    "DragonflightUICalendarButton"
}
-- 动态添加序号模式
for i=1,10 do 
    ignorePatterns[#ignorePatterns+1] = "GatherLite"..i
    ignorePatterns[#ignorePatterns+1] = "Spy_MapNoteList_mini"..i 
end

-- 检查是否为可收纳按钮
local function isCollectableButton(child)
    local name = child:GetName() or ""
    
    -- 检查是否在忽略列表中
    for _, pat in ipairs(ignorePatterns) do
        if pat:find("%.") or pat:find("%*") or pat:find("%[") then
            -- 正则表达式模式
            local success, result = pcall(string.match, name, pat)
            if success and result then return false end
        else
            -- 普通字符串匹配
            if name:find(pat, 1, true) then return false end
        end
    end
    
    local objType = child:GetObjectType()
    if objType == "Button" or objType == "CheckButton" then
        return true
    end
    
    if objType == "Frame" then
        local ok1, onclick = pcall(child.GetScript, child, "OnClick")
        local ok2, onmouseup = pcall(child.GetScript, child, "OnMouseUp")
        return (ok1 and onclick) or (ok2 and onmouseup)
    end
    
    return false
end

-- 统一Hook所有可收纳按钮的Ctrl+左键切换收纳/移出
local function HookCollectButtonCtrlToggle(child)
    if child._wanTinyCtrlToggle then return end
    local orig = child:GetScript("OnClick")
    child:SetScript("OnClick", function(self, btn)
        if btn == "LeftButton" and IsControlKeyDown() then
            local t = WanTinyDB.Map.MiniButExcludedButtons or {}
            local n = self:GetName()
            t[n] = not t[n] and true or nil
            WanTinyDB.Map.MiniButExcludedButtons = t
            RestoreAllCollectedButtons()
            ShowAllCollectedButtons()
            return
        end
        if orig then orig(self, btn) end
    end)
    child._wanTinyCtrlToggle = true
end




-- 可收纳按钮缓存（简单实现，便于后续扩展）
local collectableButtonCache = nil -- 可收纳按钮缓存

-- 事件驱动清空缓存
local function ClearCollectableButtonCache()
    collectableButtonCache = nil
end

-- 注册事件清空缓存
local cacheEventFrame = CreateFrame("Frame")
cacheEventFrame:RegisterEvent("ADDON_LOADED")
cacheEventFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
cacheEventFrame:RegisterEvent("PLAYER_LOGIN")
cacheEventFrame:RegisterEvent("PLAYER_LOGOUT")
cacheEventFrame:RegisterEvent("UI_SCALE_CHANGED")
cacheEventFrame:RegisterEvent("DISPLAY_SIZE_CHANGED")
cacheEventFrame:SetScript("OnEvent", function(self, event, ...)
    ClearCollectableButtonCache()
end)
-- 收集可收纳按钮
local function CollectCollectableButtons(useCache)
    if useCache and collectableButtonCache then
        return collectableButtonCache
    end
    local btns = {}
    local excluded = WanTinyDB.Map.MiniButExcludedButtons or {}
    for _, child in ipairs({Minimap:GetChildren()}) do
        if child:IsShown() and isCollectableButton(child) then
            HookCollectButtonCtrlToggle(child)
            local name = child:GetName()
            if not name or not excluded[name] then
                btns[#btns + 1] = child
            end
        end
    end
    collectableButtonCache = btns
    return btns
end

-- 计算布局信息
local function LayoutCollectedButtons(btns, perRow, padding)
    perRow = perRow or WanTinyDB.Map.MiniButPerRow
    padding = padding or PADDING
    local total = #btns
    if total == 0 then
        MiniMapBut.CollectFrame:SetSize(50, 30)
        return {colWidths = {}, rowHeights = {}, xOffsets = {}, yOffsets = {}, rows = 0, actualCols = 0}
    end
    local rows = math.ceil(total / perRow)
    local actualCols = math.min(total, perRow)

    local btnWidths, btnHeights = {}, {}
    for i, child in ipairs(btns) do
        local w, h = child:GetSize()
        btnWidths[i] = w or BTN_SIZE
        btnHeights[i] = h or BTN_SIZE
    end

    local colWidths, rowHeights = {}, {}
    for col = 1, actualCols do
        local maxW = 0
        for row = 1, rows do
            local idx = (row - 1) * perRow + col
            if btnWidths[idx] and btnWidths[idx] > maxW then maxW = btnWidths[idx] end
        end
        colWidths[col] = maxW
    end
    for row = 1, rows do
        local maxH = 0
        for col = 1, perRow do
            local idx = (row - 1) * perRow + col
            if btnHeights[idx] and btnHeights[idx] > maxH then maxH = btnHeights[idx] end
        end
        rowHeights[row] = maxH
    end

    local width, height = BORDER * 2 - padding, BORDER * 2 - padding
    for col = 1, actualCols do width = width + colWidths[col] + padding end
    for row = 1, rows do height = height + rowHeights[row] + padding end
    MiniMapBut.CollectFrame:SetSize(width, height)

    local xOffsets, yOffsets = {}, {}
    xOffsets[1] = BORDER
    for col = 2, actualCols do
        xOffsets[col] = xOffsets[col-1] + colWidths[col-1] + padding
    end
    yOffsets[1] = -BORDER
    for row = 2, rows do
        yOffsets[row] = yOffsets[row-1] - rowHeights[row-1] - padding
    end

    return {
        colWidths = colWidths,
        rowHeights = rowHeights,
        xOffsets = xOffsets,
        yOffsets = yOffsets,
        rows = rows,
        actualCols = actualCols
    }
end


-- 保存按钮原始状态
local function SaveButtonState(child)
    child._oldParent = child:GetParent()
    child._oldPoint = {child:GetPoint()}
    child._oldSize = {child:GetSize()}
end

-- 应用布局并设置按钮
local function ApplyCollectedButtons(btns, layout)
    local perRow = WanTinyDB.Map.MiniButPerRow
    for i, child in ipairs(btns) do
        SaveButtonState(child)
        child:SetParent(MiniMapBut.CollectFrame)
        child:ClearAllPoints()
        local col = ((i - 1) % perRow) + 1
        local row = math.floor((i - 1) / perRow) + 1
        local x = layout.xOffsets[col]
        local y = layout.yOffsets[row]
        child:SetPoint("TOPLEFT", x, y)
        child:SetAlpha(0.7)
        child:Show()
    end
    MiniMapBut.CollectFrame.buttons = btns
end

-- 展示所有收纳按钮（全局函数）
ShowAllCollectedButtons = function()
    if not MiniMapBut or not MiniMapBut.CollectFrame then return end
    RestoreAllCollectedButtons()
    local btns = CollectCollectableButtons()
    local perRow = WanTinyDB.Map.MiniButPerRow
    local padding = PADDING
    local layout = LayoutCollectedButtons(btns, perRow, padding)
    if #btns > 0 then
        ApplyCollectedButtons(btns, layout)
        -- 显示收纳面板以展示新的布局
        MiniMapBut.CollectFrame:Show()
        if WanTinyDB.Map.MiniButShouNa_YN == 1 then
            MiniMapBut.CollectFrame.hideTimer = WanTinyDB.Map.MiniButHideDelay or 1.5
            MiniMapBut.CollectFrame.isShowing = true
        end
    end
end





-- 主按钮点击分发（合并所有分支）
local function HandleButtonClick(button)
    GameTooltip:Hide()
    if button == "RightButton" then
        -- 右键打开WanTiny设置面板第1个标签页（小地图按钮收纳设置）
        if _G.WanTinyUI and _G.WanTinyUI.ToggleMainFrame then
            _G.WanTinyUI.ToggleMainFrame()
            if _G.WanTinyUI.SelectTab then _G.WanTinyUI.SelectTab(1) end
        end
    elseif IsShiftKeyDown() then
        ReloadUI()
    else
        if MiniMapBut.CollectFrame:IsShown() then
            MiniMapBut.CollectFrame:Hide()
        else
            MiniMapBut.CollectFrame:Show()
            if WanTinyDB.Map.MiniButShouNa_YN == 1 then
                MiniMapBut.CollectFrame.hideTimer = WanTinyDB.Map.MiniButHideDelay or 1.5
                MiniMapBut.CollectFrame.isShowing = true
            end
        end
    end
end

MiniMapBut:SetScript("OnClick", function(self, button)
    PlaySound(SOUNDKIT.IG_CHAT_EMOTE_BUTTON)
    HandleButtonClick(button)
end)

-- 拖拽辅助Frame
local dragFrame = CreateFrame("Frame")
dragFrame:Hide()

-- 注册/注销拖拽
local function RegisterDrag(enable)
    if enable then
        dragFrame:SetScript("OnUpdate", UpdateButtonPosition_OnUpdate)
        MiniMapBut:SetScript("OnDragStart", function(self)
            self:LockHighlight()
            dragFrame:Show()
        end)
        MiniMapBut:SetScript("OnDragStop", function(self)
            self:UnlockHighlight()
            dragFrame:Hide()
        end)
    else
        dragFrame:SetScript("OnUpdate", nil)
        MiniMapBut:SetScript("OnDragStart", nil)
        MiniMapBut:SetScript("OnDragStop", nil)
    end
end



-- 创建收纳面板

MiniMapBut.CollectFrame = CreateFrameWithBackdrop(MiniMapBut, nil, {200, 100}, {strata = "MEDIUM", movable = false, enableMouse = true, frameLevel = 1})
MiniMapBut.CollectFrame:Hide()
MiniMapBut.CollectFrame:SetFrameLevel(1)
MiniMapBut.CollectFrame.buttons = {}

-- 按钮样式配置
local buttonStyles = {
    [1] = {MAIN_BTN_SIZE, MAIN_BTN_SIZE, MAIN_BTN_ICON_SIZE, MAIN_BTN_ICON_SIZE, MAIN_BTN_BORDER_SIZE, function() -- 小地图模式
        RegisterDrag(true)
        MiniMapBut:SetParent(Minimap)
        MiniMapBut:SetHighlightTexture("Interface/Minimap/UI-Minimap-ZoomButton-Highlight")
        MiniMapBut.Border:SetDrawLayer("BORDER", 1)
        MiniMapBut.icon:SetDrawLayer("BACKGROUND", 1)
        MiniMapBut.Border:SetTexture("Interface/Minimap/MiniMap-TrackingBorder")
        MiniMapBut.Border:SetSize(MAIN_BTN_BORDER_SIZE, MAIN_BTN_BORDER_SIZE)
        MiniMapBut.Border:ClearAllPoints()
        MiniMapBut.Border:SetPoint("TOPLEFT", -1, 0)
        MiniMapBut.Border:Show()
        MiniMapBut.CollectFrame:SetPoint("TOPRIGHT", MiniMapBut, "BOTTOMLEFT", -2, 20)
        UpdateButtonPosition(1)
    end},
    [2] = {27, 26, 17, 17, 0, function() -- 聊天框模式
        MiniMapBut.Border:Hide()
        MiniMapBut:ClearAllPoints()
        if ChatFrameChannelButton then
            MiniMapBut:SetPoint("BOTTOM", ChatFrameChannelButton, "TOP", 0, 40)
        else
            MiniMapBut:SetPoint("BOTTOMLEFT", ChatFrame1, "TOPLEFT", 0, 2)
        end
        MiniMapBut:SetNormalAtlas("chatframe-button-up")
        MiniMapBut:SetPushedAtlas("chatframe-button-down")
        MiniMapBut:SetHighlightAtlas("chatframe-button-highlight")
        MiniMapBut.icon:SetDrawLayer("ARTWORK", 1)
        MiniMapBut.CollectFrame:SetPoint("BOTTOMLEFT", MiniMapBut, "TOPRIGHT", 2, 2)
        MiniMapBut:SetParent(UIParent)
    end},
    [3] = {MAIN_BTN_SIZE, MAIN_BTN_SIZE, MAIN_BTN_ICON_SIZE, MAIN_BTN_ICON_SIZE, 0, function() -- 自由模式
        RegisterDrag(true)
        MiniMapBut:SetParent(UIParent)
        MiniMapBut.Border:Hide()
        MiniMapBut:SetHighlightTexture("Interface/Buttons/ButtonHilight-Square")
        MiniMapBut.CollectFrame:SetPoint("TOPRIGHT", MiniMapBut, "BOTTOMLEFT", -2, 20)
        UpdateButtonPosition(3)
    end}
}

-- 设置主按钮样式
function MiniMapBut:SetButtonStyle(mode)
    mode = mode or WanTinyDB.Map.MinimapPointMode
    local style = buttonStyles[mode]
    if not style then return end
    
    -- 清理状态
    MiniMapBut.CollectFrame:ClearAllPoints()
    MiniMapBut:ClearNormalTexture()
    MiniMapBut:ClearPushedTexture()
    RegisterDrag(false)
    
    -- 设置通用属性
    MiniMapBut.offset = style[5]
    MiniMapBut:SetFrameStrata("MEDIUM")
    MiniMapBut:EnableMouse(true)
    MiniMapBut:SetSize(style[1], style[2])
    MiniMapBut.icon:SetSize(style[3], style[4])
    
    -- 执行特定样式设置
    style[6]()
end



-- 设置收纳面板内按钮透明度
local function SetCollectFrameButtonsAlpha(alpha)
    local buttons = MiniMapBut.CollectFrame.buttons
    if buttons then
        for _, btn in ipairs(buttons) do
            if btn and btn:IsShown() then
                btn:SetAlpha(alpha)
            end
        end
    end
end

-- 收纳面板鼠标悬停处理
local function HandleCollectFrameHover(isEnter)
    SetCollectFrameButtonsAlpha(isEnter and 1 or 0.7)
    if isEnter then
        MiniMapBut.CollectFrame.isShowing = nil
    elseif WanTinyDB.Map.MiniButShouNa_YN == 1 then
        MiniMapBut.CollectFrame.hideTimer = WanTinyDB.Map.MiniButHideDelay or 1.5
        MiniMapBut.CollectFrame.isShowing = true
    end
end

MiniMapBut.CollectFrame:SetScript("OnEnter", function(self) HandleCollectFrameHover(true) end)
MiniMapBut.CollectFrame:SetScript("OnLeave", function(self) HandleCollectFrameHover(false) end)

MiniMapBut.CollectFrame:SetScript("OnUpdate", function(self, elapsed)
    if self.isShowing and not self:IsMouseOver() and self.hideTimer then
        self.hideTimer = self.hideTimer - elapsed
        if self.hideTimer <= 0 then
            self:Hide()
            self.isShowing, self.hideTimer = nil, nil
        end
    end
end)

-- 模块初始化函数
local function InitButtonStorage()
    InitConfig()
    MiniMapBut:Show()
    
    -- 延迟初始化，确保游戏完全加载
    C_Timer.After(0.5, function()
        MiniMapBut:SetButtonStyle()
        if WanTinyDB.Map.MiniButShouNa_YN == 1 then
            C_Timer.After(1, function()
                ShowAllCollectedButtons()
                MiniMapBut.CollectFrame:Hide()
            end)
        end
    end)
end

-- 调用初始化函数
InitButtonStorage()

-- 暴露关键函数为全局变量供WanTinyUI.lua访问
_G.WanTiny_RestoreAllCollectedButtons = RestoreAllCollectedButtons
_G.WanTiny_ShowAllCollectedButtons = ShowAllCollectedButtons

end) -- WanTiny_RegisterModule 结束

