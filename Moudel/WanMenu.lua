-- WanMenu 快捷标记模块
_G.Wan<PERSON>u = _G<PERSON> or {}
local WanMenu = _G.Wan<PERSON>

if _G.WanTiny_RegisterModule then
    _G.WanTiny_RegisterModule("WanMenu", function() WanMenu.Initialize() end)
else
    error("WanMenu requires WanTiny framework to run!")
end

local function GetConfig()
    _G.WanTinyDB = _G.WanTinyDB or {}
    _G.WanTinyDB.config = _G.WanTinyDB.config or {}
    _G.WanTinyDB.config.WanMenu = _G.WanTinyDB.config.WanMenu or {}
    return _G.WanTinyDB.config.WanMenu
end

WanMenu.GetConfig = GetConfig

local ICON_SIZE, BUTTON_SPACING = 28, 5
local pullTimes, pullTimeIdx = {3, 5, 10}, 1
local markFrame

local function doPullCountdown(t)
    local cfg = GetConfig()
    local time = cfg.defaultCountdown or t or 5
    if C_PartyInfo and C_PartyInfo.DoCountdown then 
        C_PartyInfo.DoCountdown(time)
    else 
        SendChatMessage("开怪倒数 "..time.." 秒！", IsInRaid() and "RAID_WARNING" or "PARTY") 
    end
end

local function CreateMarkFrame()
    if markFrame then return markFrame end
    
    local frame = CreateFrame("Frame", nil, UIParent)
    frame:SetSize(13 * ICON_SIZE + 12 * BUTTON_SPACING, ICON_SIZE)
    frame:SetPoint("TOP", UIParent, "TOP", 0, -50)
    frame:SetAlpha(0.9)
    frame:EnableMouse(true)
    frame:SetMovable(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetFrameStrata("MEDIUM")
    frame:Show()
    markFrame = frame
    
    local buttons = {
        {"重置", "|cffFFD93D重置副本|r", function() ResetInstances() end},
        {"清除", "|cffFFD93D清除标记|r", function() 
            for i=1,8 do SetRaidTarget("player", i) end
            C_Timer.After(0.3, function() SetRaidTarget("player", 0) end)
        end},
        {"职责", "|cffFFD93D职责就位|r", function() InitiateRolePoll() end},
        {"就位", "|cffFFD93D就位确认|r", function() DoReadyCheck() end},
        {"倒数", "|cffFFD93D开怪倒数|r", function() doPullCountdown() end}
    }
    
    local raidIcons = {"|cffFFFF00星星|r", "|cffFF8C00大饼|r", "|cffA335EE紫菱|r", "|cff0070DD三角|r", "|cffFFFFFF月亮|r", "|cff1EFF00方块|r", "|cffFF0000叉叉|r", "|cffFFFFFF骷髅|r"}
    for i = 1, 8 do
        table.insert(buttons, 3 + i, {i, raidIcons[i], function() SetRaidTarget("target", i) end, true})
    end
    
    frame.buttons = {}

    for i, info in ipairs(buttons) do
        local btn = CreateFrame("Button", nil, frame)
        btn:SetSize(ICON_SIZE, ICON_SIZE)
        btn:SetPoint("LEFT", frame, "LEFT", (i-1) * (ICON_SIZE + BUTTON_SPACING), 0)
        
        if info[4] then -- 是图标按钮
            local icon = btn:CreateTexture(nil, "ARTWORK")
            icon:SetAllPoints()
            icon:SetTexture("Interface\\TargetingFrame\\UI-RaidTargetingIcon_"..info[1])
            btn.icon = icon
        else -- 是文字按钮
            local text = btn:CreateFontString(nil, "OVERLAY", "GameFontNormal")
            text:SetText(info[1])
            text:SetPoint("CENTER")
            btn.text = text
        end
        
        if info[1] == "倒数" then
            btn:SetScript("OnClick", function(self, button)
                if button == "RightButton" then
                    -- 右键打开设置
                    if _G.WanTinyUI and _G.WanTinyUI.ToggleMainFrame then
                        _G.WanTinyUI.ToggleMainFrame()
                        if _G.WanTinyUI.SelectTab then
                            _G.WanTinyUI.SelectTab(2) -- 选择第二个标签页
                        end
                    end
                else
                    info[3]()
                end
            end)
            btn:RegisterForClicks("LeftButtonUp", "RightButtonUp")
        else
            btn:SetScript("OnClick", info[3])
        end
        btn:SetScript("OnEnter", function(self)
            GameTooltip:SetOwner(self, "ANCHOR_BOTTOM")
            GameTooltip:SetText(info[2], 1, 1, 1)
            GameTooltip:Show()
        end)
        btn:SetScript("OnLeave", function() GameTooltip:Hide() end)
        
        frame.buttons[i] = btn
    end
    
    return frame
end

local function UpdateMarkFrame()
    if not markFrame then return end
    
    local cfg = GetConfig()
    markFrame:SetShown(cfg.wanmarkEnable ~= false)
    
    -- 更新缩放
    local scale = cfg.markScale or 1.0
    markFrame:SetScale(scale)
    
    -- 更新按钮显示
    for _, btn in ipairs(markFrame.buttons) do
        if btn.text then
            btn:SetShown(cfg.wanmarkTextBtn ~= false)
        elseif btn.icon then
            btn:SetShown(cfg.wanmarkIconBtn ~= false)
        end
    end
    
    -- 更新位置（优先使用保存的位置，无论锁定状态如何）
    markFrame:ClearAllPoints()
    if cfg.markPos then
        local pos = cfg.markPos
        markFrame:SetPoint(pos.point or "CENTER", UIParent, pos.relPoint or "CENTER", pos.x or 0, pos.y or 0)
    else
        markFrame:SetPoint("TOP", UIParent, "TOP", 0, -50)
    end
    
    -- 设置拖拽（锁定逻辑：true=锁定，false=可移动）
    markFrame:SetMovable(cfg.wanmarkFollow ~= true)
    if cfg.wanmarkFollow ~= true then
        markFrame:SetScript("OnDragStart", function(self)
            if IsAltKeyDown() then self:StartMoving() end
        end)
        markFrame:SetScript("OnDragStop", function(self)
            self:StopMovingOrSizing()
            local point, _, relPoint, x, y = self:GetPoint()
            cfg.markPos = {point=point, relPoint=relPoint, x=x, y=y}
        end)
    else
        markFrame:SetScript("OnDragStart", nil)
        markFrame:SetScript("OnDragStop", nil)
    end
end

function WanMenu.Initialize()
    local config = GetConfig()
    -- 设置默认值
    if config.wanmarkEnable == nil then config.wanmarkEnable = true end
    if config.wanmarkTextBtn == nil then config.wanmarkTextBtn = true end
    if config.wanmarkIconBtn == nil then config.wanmarkIconBtn = true end
    if config.wanmarkFollow == nil then config.wanmarkFollow = true end
    if config.defaultCountdown == nil then config.defaultCountdown = 10 end
    if config.markScale == nil then config.markScale = 1.0 end
    
    CreateMarkFrame()
    UpdateMarkFrame()
end

function WanMenu.UpdateFromConfig(key, value)
    if markFrame then
        UpdateMarkFrame()
    end
end



