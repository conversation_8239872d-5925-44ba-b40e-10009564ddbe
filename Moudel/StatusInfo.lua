WanTiny_RegisterModule("StatusInfo", function()
-- 确保数据库初始化
local StatusInfoDB = StatusInfoDB or { alignMode = "RIGHT", point = "CENTER", relativePoint = "CENTER", xOfs = 0, yOfs = 0 }
_G.StatusInfoDB = StatusInfoDB

local NameFont = GameTooltipTextLeft1 and GameTooltipTextLeft1:GetFont() or "Fonts\\FRIZQT__.TTF"
local NumbFS, ySpacing, xPad = 16, 2, 50
local NumbFSMain, NumbFSMain1 = NumbFS * 2, NumbFS * 1.4
local NumbFSother, NumbFSother1 = NumbFS * 1.2, NumbFS * 1.2
local FontF = "THINOUTLINE"

local function CreateFS(parent, size, outline)
    local fs = parent:CreateFontString(nil, 'OVERLAY')
    fs:SetFont(NameFont, size, outline)
    return fs
end

local StatuFrame = CreateFrame("Frame", "StatusInfoFrame", UIParent)
StatuFrame:SetSize(150, 120)
StatuFrame:SetAlpha(0.8)
StatuFrame:SetMovable(true)
StatuFrame:EnableMouse(true)
StatuFrame:SetClampedToScreen(true)

local isUnlocked, mouseDownX, mouseDownY, mouseDownTime = false, 0, 0, 0

local border = CreateFrame("Frame", nil, StatuFrame, BackdropTemplateMixin and "BackdropTemplate")
border:SetAllPoints(StatuFrame)
border:SetFrameLevel(StatuFrame:GetFrameLevel() + 10)
border:SetBackdrop({ edgeFile = "Interface/Tooltips/UI-Tooltip-Border", edgeSize = 12 })
border:SetBackdropBorderColor(1, 0, 0, 1)
border:Hide()

local function UpdateLockState()
    if isUnlocked then
        border:Show()
        StatuFrame:RegisterForDrag("LeftButton")
    else
        border:Hide()
        StatuFrame:RegisterForDrag()
    end
end

local alignMode
local statuMain      = CreateFS(StatuFrame, NumbFSMain, FontF)
local statuMainDes   = CreateFS(StatuFrame, NumbFSMain1, FontF)
local statu2         = CreateFS(StatuFrame, NumbFSother, FontF)
local statu2Des      = CreateFS(StatuFrame, NumbFSother1, FontF)
local statu3         = CreateFS(StatuFrame, NumbFSother, FontF)
local statu3Des      = CreateFS(StatuFrame, NumbFSother1, FontF)
local statu4         = CreateFS(StatuFrame, NumbFSother, FontF)
local statu4Des      = CreateFS(StatuFrame, NumbFSother1, FontF)
local statu5         = CreateFS(StatuFrame, NumbFSother, FontF)
local statu5Des      = CreateFS(StatuFrame, NumbFSother1, FontF)
local statu6         = CreateFS(StatuFrame, NumbFSother, FontF)
local statu6Des      = CreateFS(StatuFrame, NumbFSother1, FontF)
local function SetAlignMode(mode)
    alignMode = mode
    if alignMode == "RIGHT" then
        -- 主属性
        statuMain:ClearAllPoints()
        statuMain:SetPoint('TOPRIGHT', StatuFrame, 'TOPRIGHT', -xPad, 0)
        statuMain:SetJustifyH('RIGHT')
        statuMainDes:ClearAllPoints()
        statuMainDes:SetPoint('LEFT', statuMain, 'RIGHT', 8, 0)
        statuMainDes:SetJustifyH('LEFT')
        -- 2
        statu2:ClearAllPoints()
        statu2:SetPoint('TOPRIGHT', statuMain, 'BOTTOMRIGHT', 0, ySpacing)
        statu2:SetJustifyH('RIGHT')
        statu2Des:ClearAllPoints()
        statu2Des:SetPoint('LEFT', statu2, 'RIGHT', 8, 0)
        statu2Des:SetJustifyH('LEFT')
        -- 3
        statu3:ClearAllPoints()
        statu3:SetPoint('TOPRIGHT', statu2, 'BOTTOMRIGHT', 0, ySpacing)
        statu3:SetJustifyH('RIGHT')
        statu3Des:ClearAllPoints()
        statu3Des:SetPoint('LEFT', statu3, 'RIGHT', 8, 0)
        statu3Des:SetJustifyH('LEFT')
        -- 4
        statu4:ClearAllPoints()
        statu4:SetPoint('TOPRIGHT', statu3, 'BOTTOMRIGHT', 0, ySpacing)
        statu4:SetJustifyH('RIGHT')
        statu4Des:ClearAllPoints()
        statu4Des:SetPoint('LEFT', statu4, 'RIGHT', 8, 0)
        statu4Des:SetJustifyH('LEFT')
        -- 5
        statu5:ClearAllPoints()
        statu5:SetPoint('TOPRIGHT', statu4, 'BOTTOMRIGHT', 0, ySpacing)
        statu5:SetJustifyH('RIGHT')
        statu5Des:ClearAllPoints()
        statu5Des:SetPoint('LEFT', statu5, 'RIGHT', 8, 0)
        statu5Des:SetJustifyH('LEFT')
        -- 6
        statu6:ClearAllPoints()
        statu6:SetPoint('TOPRIGHT', statu5, 'BOTTOMRIGHT', 0, ySpacing)
        statu6:SetJustifyH('RIGHT')
        statu6Des:ClearAllPoints()
        statu6Des:SetPoint('LEFT', statu6, 'RIGHT', 8, 0)
        statu6Des:SetJustifyH('LEFT')
    else
        -- 主属性
        statuMain:ClearAllPoints()
        statuMain:SetPoint('TOPLEFT', StatuFrame, 'TOPLEFT', xPad, 0)
        statuMain:SetJustifyH('LEFT')
        statuMainDes:ClearAllPoints()
        statuMainDes:SetPoint('RIGHT', statuMain, 'LEFT', -8, 0)
        statuMainDes:SetJustifyH('RIGHT')
        -- 2
        statu2:ClearAllPoints()
        statu2:SetPoint('TOPLEFT', statuMain, 'BOTTOMLEFT', 0, ySpacing)
        statu2:SetJustifyH('LEFT')
        statu2Des:ClearAllPoints()
        statu2Des:SetPoint('RIGHT', statu2, 'LEFT', -8, 0)
        statu2Des:SetJustifyH('RIGHT')
        -- 3
        statu3:ClearAllPoints()
        statu3:SetPoint('TOPLEFT', statu2, 'BOTTOMLEFT', 0, ySpacing)
        statu3:SetJustifyH('LEFT')
        statu3Des:ClearAllPoints()
        statu3Des:SetPoint('RIGHT', statu3, 'LEFT', -8, 0)
        statu3Des:SetJustifyH('RIGHT')
        -- 4
        statu4:ClearAllPoints()
        statu4:SetPoint('TOPLEFT', statu3, 'BOTTOMLEFT', 0, ySpacing)
        statu4:SetJustifyH('LEFT')
        statu4Des:ClearAllPoints()
        statu4Des:SetPoint('RIGHT', statu4, 'LEFT', -9, 0)
        statu4Des:SetJustifyH('RIGHT')
        -- 5
        statu5:ClearAllPoints()
        statu5:SetPoint('TOPLEFT', statu4, 'BOTTOMLEFT', 0, ySpacing)
        statu5:SetJustifyH('LEFT')
        statu5Des:ClearAllPoints()
        statu5Des:SetPoint('RIGHT', statu5, 'LEFT', -9, 0)
        statu5Des:SetJustifyH('RIGHT')
        -- 6
        statu6:ClearAllPoints()
        statu6:SetPoint('TOPLEFT', statu5, 'BOTTOMLEFT', 0, ySpacing)
        statu6:SetJustifyH('LEFT')
        statu6Des:ClearAllPoints()
        statu6Des:SetPoint('RIGHT', statu6, 'LEFT', -8, 0)
        statu6Des:SetJustifyH('RIGHT')
    end
end

local function InitAlignMode()
    alignMode = StatusInfoDB.alignMode
    if alignMode ~= "LEFT" and alignMode ~= "RIGHT" then
        alignMode = "RIGHT"
        StatusInfoDB.alignMode = alignMode
    end
    SetAlignMode(alignMode)
end

-- 移除原来的事件监听器，直接在模块初始化时调用
-- 因为模块是在 PLAYER_LOGIN 之后才初始化的，所以可以直接初始化
InitAlignMode()



StatuFrame:SetScript("OnMouseDown", function(self, button)
    if button == "LeftButton" then
        mouseDownTime = GetTime()
        mouseDownX, mouseDownY = GetCursorPosition()
    elseif button == "RightButton" and isUnlocked then
        if alignMode == "RIGHT" then
            alignMode = "LEFT"
        else
            alignMode = "RIGHT"
        end
        StatusInfoDB.alignMode = alignMode
        SetAlignMode(alignMode)
    end
end)

StatuFrame:SetScript("OnMouseUp", function(self, button)
    if button == "LeftButton" then
        local upX, upY = GetCursorPosition()
        local upTime = GetTime()
        local moved = (math.abs(upX - mouseDownX) > 4) or (math.abs(upY - mouseDownY) > 4)
        local isClick = not moved and (upTime - mouseDownTime < 0.5)
        if IsAltKeyDown() and isClick then
            isUnlocked = not isUnlocked
            UpdateLockState()
        end
    end
end)

StatuFrame:SetScript("OnDragStart", function(self)
    if isUnlocked then
        self:StartMoving()
    end
end)

StatuFrame:SetScript("OnDragStop", function(self)
    self:StopMovingOrSizing()
    if isUnlocked then
        local point, relativeTo, relativePoint, xOfs, yOfs = self:GetPoint()
        StatusInfoDB.point = point
        StatusInfoDB.relativePoint = relativePoint
        StatusInfoDB.xOfs = xOfs
        StatusInfoDB.yOfs = yOfs
    end
end)
local function GetCurrentInfo()
    local tplayerClass = string.upper(select(2, UnitClass("player")))
    local PlayAs = 1
    -- 先根据职业和形态初步分类
    if tplayerClass == "DEATHKNIGHT" then
        if select(2, UnitAura("player", 1, "HELPFUL")) == 48263 then
            PlayAs = 4
        else
            PlayAs = 1
        end
    elseif tplayerClass == "DRUID" then
        local form = GetShapeshiftFormID()
        if form == 8 then
            PlayAs = 4
        else
            -- 猫/人形/平衡/恢复，进一步判断
            local spell = math.max(GetSpellBonusDamage(3), GetSpellBonusDamage(4), GetSpellBonusDamage(5), GetSpellBonusDamage(6), GetSpellBonusDamage(7))
            local base, posBuff, negBuff = UnitAttackPower("player")
            local ap = base + posBuff + negBuff
            if spell > ap then
                PlayAs = 2 -- 法系
            else
                PlayAs = 1 -- 物理
            end
        end
    elseif tplayerClass == "PALADIN" then
        -- 奶骑/惩戒/防骑，简单判断
        local spell = math.max(GetSpellBonusDamage(3), GetSpellBonusDamage(4), GetSpellBonusDamage(5), GetSpellBonusDamage(6), GetSpellBonusDamage(7))
        local base, posBuff, negBuff = UnitAttackPower("player")
        local ap = base + posBuff + negBuff
        if spell > ap then
            PlayAs = 3 -- 奶骑
        else
            PlayAs = 1 -- 物理
        end
    elseif tplayerClass == "SHAMAN" then
        local spell = math.max(GetSpellBonusDamage(3), GetSpellBonusDamage(4), GetSpellBonusDamage(5), GetSpellBonusDamage(6), GetSpellBonusDamage(7))
        local base, posBuff, negBuff = UnitAttackPower("player")
        local ap = base + posBuff + negBuff
        if spell > ap then
            PlayAs = 2 -- 法系
        else
            PlayAs = 1 -- 增强/物理
        end
    elseif tplayerClass == "PRIEST" then
        -- 暗牧/奶
        local shadow = GetSpellBonusDamage(6)
        local heal = GetSpellBonusHealing()
        if shadow > heal then
            PlayAs = 2 -- 暗影
        else
            PlayAs = 3 -- 奶
        end
    elseif tplayerClass == "WARRIOR" then
        local form = GetShapeshiftFormID()
        if form == 18 then
            PlayAs = 4
        else
            PlayAs = 1
        end
    elseif tplayerClass == "ROGUE" then
        PlayAs = 1
    elseif tplayerClass == "HUNTER" then
        PlayAs = 5
    elseif tplayerClass == "MAGE" or tplayerClass == "WARLOCK" then
        PlayAs = 2
    end
    return PlayAs
end

local function updateStatu(playas)
    if playas == 1 then -- 物理DPS
        local base, posBuff, negBuff = UnitAttackPower("player")
        local effective = base + posBuff + negBuff
        statuMain:SetText("|cffff3333" .. effective)
        statuMainDes:SetText("|cffff5522攻强")
        statu2:SetText(format("|cffffffcc%.2f%%", GetCombatRatingBonus(18) or 0))
        statu2Des:SetText("|cffffcc00急速")
        statu3:SetText(format("|cffffffcc%.2f%%", GetCritChance()))
        statu3Des:SetText("|cffff9900暴击")
        statu4:SetText(format("|cffffffcc%.2f%%", GetCombatRatingBonus(12) or 0))
        statu4Des:SetText("|cff3399ff命中")
        local mainSpeed, offSpeed = UnitAttackSpeed("player")
        if offSpeed then
            statu5:SetText(format("|cffffff66%.2f/%.2f", mainSpeed, offSpeed))
        else
            statu5:SetText(format("|cffffff66%.2f", mainSpeed))
        end
        statu5Des:SetText("|cffb266ff攻速")
    elseif playas == 2 then -- 法系DPS
        local SpellDamage = math.max(GetSpellBonusDamage(3), GetSpellBonusDamage(4), GetSpellBonusDamage(5), GetSpellBonusDamage(6), GetSpellBonusDamage(7))
        statuMain:SetText("|cffff3333" .. SpellDamage)
        statuMainDes:SetText("|cff66ccff法伤")
        statu2:SetText(format("|cffffff66%.2f%%", GetCombatRatingBonus(20) or 0))
        statu2Des:SetText("|cffffcc00急速")
        statu3:SetText(format("|cffffff66%.2f%%", GetSpellCritChance(7) or 0))
        statu3Des:SetText("|cffff9900暴击")
        statu4:SetText(format("|cffffff66%.2f%%", GetCombatRatingBonus(8) or 0))
        statu4Des:SetText("|cff3399ff命中")
        local base, casting = GetManaRegen()
        statu5:SetText(format("|cff0066cc%d/%d", base * 5, casting * 5))
        statu5Des:SetText("|cff0066cc法回")
    elseif playas == 3 then -- 治疗
        statuMain:SetText("|cff00ff00" .. GetSpellBonusHealing())
        statuMainDes:SetText("|cff00ff00法强")
        statu2:SetText(format("|cffffff33%.2f%%", GetCombatRatingBonus(20) or 0))
        statu2Des:SetText("|cffffcc00急速")
        statu3:SetText(format("|cffffffcc%.2f%%", GetSpellCritChance(2) or 0))
        statu3Des:SetText("|cffff9900暴击")
        statu4:SetText(format("|cffffff66%.2f%%", GetCombatRatingBonus(8) or 0))
        statu4Des:SetText("|cff3399ff命中")
        local base, casting = GetManaRegen()
        statu5:SetText(format("|cff0066cc%d/%d", base * 5, casting * 5))
        statu5Des:SetText("|cff0066cc法回")
    elseif playas == 4 then -- 坦克
        local baseArmor, effectiveArmor = UnitArmor("player")
        statuMain:SetText("|cff0066cc" .. effectiveArmor)
        statuMainDes:SetText("|cffc79c6e护甲")
        statu2:SetText(format("|cffffffcc%.2f%%", GetDodgeChance()))
        statu2Des:SetText("躲闪")
        statu3:SetText(format("|cffcc99cc%.2f%%", GetParryChance()))
        statu3Des:SetText("招架")
        statu4:SetText(format("|cffffff33%.2f%%", GetBlockChance()))
        statu4Des:SetText("格挡")
        statu5:SetText(format("|cffffff33%d", UnitHealthMax("player")))
        statu5Des:SetText("|cffff2222血量")
    elseif playas == 5 then -- 猎人
        local base, posBuff, negBuff = UnitRangedAttackPower("player")
        local effective = base + posBuff + negBuff
        statuMain:SetText("|cffff3333" .. effective)
        statuMainDes:SetText("|cffff5522攻强")
        statu2:SetText(format("|cff00ff00 %.2f%%", GetCombatRatingBonus(18) or 0))
        statu2Des:SetText("|cffffcc00急速")
        statu3:SetText(format("|cffffffcc %.2f%%", GetRangedCritChance()))
        statu3Des:SetText("|cffff9900暴击")
        statu4:SetText(format("|cffffffcc %.2f%%", GetCombatRatingBonus(9) or 0))
        statu4Des:SetText("|cff3399ff命中")
        local speed = select(1, UnitRangedDamage("player"))
        statu5:SetText(format("|cffffff66%.2f", speed))
        statu5Des:SetText("|cffb266ff攻速")
    end

    -- 所有职业都显示
    local MSpercent = math.floor((GetUnitSpeed and GetUnitSpeed("player") or 0) / 7 * 100 + 0.5)
    statu6:SetText("|cff00ffff" .. MSpercent)
    statu6Des:SetText("|Cffff00ff移速")
end

local function Refresh()
    local playas = GetCurrentInfo()
    updateStatu(playas)
end

StatuFrame:RegisterEvent("ACTIVE_TALENT_GROUP_CHANGED")
StatuFrame:RegisterEvent("PLAYER_TALENT_UPDATE")
StatuFrame:RegisterEvent("UPDATE_SHAPESHIFT_FORM")
StatuFrame:RegisterEvent("PLAYER_REGEN_DISABLED")
StatuFrame:RegisterEvent("PLAYER_REGEN_ENABLED")
StatuFrame:RegisterEvent("UNIT_AURA")
StatuFrame:SetScript("OnEvent", function(self, event)
    if event == "UNIT_AURA" or event == "UPDATE_SHAPESHIFT_FORM" or event == "ACTIVE_TALENT_GROUP_CHANGED" or event == "PLAYER_TALENT_UPDATE" then
        Refresh()
    elseif event == "PLAYER_REGEN_DISABLED" then
        StatuFrame:SetAlpha(1)
    elseif event == "PLAYER_REGEN_ENABLED" then
        StatuFrame:SetAlpha(0.5)
    end
end)


local TimeSinceLastUpdate = 0
StatuFrame:SetScript("OnUpdate", function(self, elapsed)
    TimeSinceLastUpdate = TimeSinceLastUpdate + elapsed
    if TimeSinceLastUpdate > 0.5 then
        Refresh()
        TimeSinceLastUpdate = 0
    end
end)

-- 设置初始位置并进行第一次刷新
StatuFrame:ClearAllPoints()
if StatusInfoDB.point and StatusInfoDB.xOfs and StatusInfoDB.yOfs then
    StatuFrame:SetPoint(StatusInfoDB.point, UIParent, StatusInfoDB.relativePoint or StatusInfoDB.point, StatusInfoDB.xOfs, StatusInfoDB.yOfs)
else
    StatuFrame:SetPoint("CENTER", 0, 0)
end

Refresh()

end)